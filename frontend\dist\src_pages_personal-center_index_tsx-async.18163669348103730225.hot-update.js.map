{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.18163669348103730225.hot-update.js", "src/pages/personal-center/index.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='2267841925241796685';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/team-management/index.tsx\":[\"src/pages/team-management/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import { useModel } from '@umijs/max';\nimport { Card, Col, Row, Spin } from 'antd';\nimport React from 'react';\nimport UserFloatButton from '@/components/FloatButton';\nimport TeamListCard from './TeamListCard';\nimport TodoManagement from './TodoManagement';\nimport UserProfileCard from './UserProfileCard';\n\n/**\n * 个人中心页面组件\n *\n * 这是用户的个人中心主页面，提供用户信息管理、团队管理、待办事项等功能。\n * 是用户进行个人设置和团队操作的主要入口页面。\n *\n * 页面功能：\n * 1. 用户个人信息展示和编辑\n * 2. 团队列表显示和团队切换\n * 3. 个人待办事项管理\n * 4. 全局浮动操作按钮\n *\n * 页面结构：\n * - 顶部：用户个人信息卡片（全宽显示）\n * - 左侧：待办事项管理（响应式布局）\n * - 右侧：团队列表和操作（响应式布局）\n * - 浮动：全局操作按钮\n *\n * 权限控制：\n * - 需要用户登录才能访问\n * - 自动检查登录状态并重定向\n * - 支持登录状态变化的实时响应\n *\n * 响应式设计：\n * - 移动端：垂直堆叠布局\n * - 桌面端：左右分栏布局\n * - 自适应不同屏幕尺寸\n */\nconst PersonalCenterPage: React.FC = () => {\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户信息和加载状态：\n   * - initialState: 包含用户和团队信息的全局状态\n   * - loading: 全局状态的加载状态\n   */\n  const { initialState, loading } = useModel('@@initialState');\n\n  // 设置模态框状态\n  const [settingsModalVisible, setSettingsModalVisible] = useState(false);\n\n  /**\n   * 加载状态处理\n   *\n   * 当全局状态正在初始化时，显示加载界面。\n   * 这确保了用户在状态加载完成前看到友好的加载提示。\n   */\n  if (loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>\n      </div>\n    );\n  }\n\n  /**\n   * 登录状态检查已由应用级路由守卫处理\n   *\n   * 移除页面级别的登录检查，避免与应用级路由守卫冲突。\n   * 应用级路由守卫在 app.tsx 中的 onPageChange 函数已经处理了\n   * 用户登录状态检查和重定向逻辑，无需在页面组件中重复检查。\n   *\n   * 这样可以避免登录成功后的状态更新时序问题，确保用户\n   * 一次登录成功后能够正常访问个人中心页面。\n   */\n\n  return (\n    <>\n      {/* 页面主容器 */}\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff', // 浅蓝色背景，营造清新的视觉效果\n          padding: '12px 12px 24px 12px', // 移动端优化：减少左右边距，增加底部边距\n        }}\n      >\n        {/*\n         * 主内容卡片容器\n         *\n         * 使用Card组件作为主要内容的容器，提供：\n         * 1. 统一的视觉边界和阴影效果\n         * 2. 响应式的内边距设置\n         * 3. 圆角设计提升视觉体验\n         * 4. 全高度布局适配不同屏幕\n         */}\n        <Card\n          style={{\n            width: '100%',\n            minHeight: 'calc(100vh - 48px)', // 减去外层padding的高度\n            borderRadius: '12px', // 圆角设计\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)', // 轻微阴影效果\n          }}\n          styles={{\n            body: {\n              padding: '24px', // 内容区域的内边距\n            },\n          }}\n        >\n          {/*\n           * 响应式网格布局\n           *\n           * 使用Ant Design的Row/Col组件实现响应式布局：\n           * - 移动端：垂直堆叠，所有组件占满宽度\n           * - 桌面端：个人信息全宽，待办事项和团队列表左右分栏\n           * - gutter: 组件间距设置\n           * - margin: 0: 避免Row组件的默认负边距影响布局\n           */}\n          <Row gutter={[16, 16]} style={{ margin: 0 }}>\n            {/*\n             * 个人信息卡片区域\n             *\n             * 显示用户的基本信息、头像、联系方式等。\n             * 全宽显示，作为页面的头部信息区域。\n             */}\n            <Col xs={24} style={{ marginBottom: 8 }}>\n              <UserProfileCard />\n            </Col>\n\n            {/*\n             * 待办事项管理区域\n             *\n             * 个人待办事项的管理界面，支持添加、编辑、删除待办事项。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据左半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n              style={{ marginBottom: 8 }}\n            >\n              <TodoManagement />\n            </Col>\n\n            {/*\n             * 团队列表管理区域\n             *\n             * 显示用户所属的团队列表，支持团队切换和创建新团队。\n             * 这是团队管理功能的主要入口。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据右半部分\n             */}\n            <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>\n              <TeamListCard />\n            </Col>\n          </Row>\n        </Card>\n      </div>\n\n      {/*\n       * 全局浮动操作按钮\n       *\n       * 提供快速访问常用功能的浮动按钮，如：\n       * - 快速创建团队\n       * - 用户设置\n       * - 帮助信息\n       *\n       * 位置固定在页面右下角，不受页面滚动影响。\n       */}\n      <UserFloatButton />\n\n\n\n      {/* 设置模态框 */}\n      <SettingsModal\n        open={settingsModalVisible}\n        onClose={() => setSettingsModalVisible(false)}\n      />\n    </>\n  );\n};\n\nexport default PersonalCenterPage;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCC+Lb;;;2BAAA;;;;;;;wCAlMyB;yCACY;mFACnB;yFACU;0FACH;4FACE;6FACC;;;;;;;;;;YAE5B;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GACD,MAAM,qBAA+B;;gBACnC;;;;;;GAMC,GACD,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAE3C,UAAU;gBACV,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,SAAS;gBAEjE;;;;;GAKC,GACD,IAAI,SACF,qBACE,2BAAC;oBACC,OAAO;wBACL,WAAW;wBACX,YAAY;wBACZ,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;;sCAEA,2BAAC,UAAI;4BAAC,MAAK;;;;;;sCACX,2BAAC;4BAAI,OAAO;gCAAE,YAAY;4BAAG;sCAAG;;;;;;;;;;;;gBAKtC;;;;;;;;;GASC,GAED,qBACE;;sCAEE,2BAAC;4BACC,OAAO;gCACL,WAAW;gCACX,YAAY;gCACZ,SAAS;4BACX;sCAWA,cAAA,2BAAC,UAAI;gCACH,OAAO;oCACL,OAAO;oCACP,WAAW;oCACX,cAAc;oCACd,WAAW;gCACb;gCACA,QAAQ;oCACN,MAAM;wCACJ,SAAS;oCACX;gCACF;0CAWA,cAAA,2BAAC,SAAG;oCAAC,QAAQ;wCAAC;wCAAI;qCAAG;oCAAE,OAAO;wCAAE,QAAQ;oCAAE;;sDAOxC,2BAAC,SAAG;4CAAC,IAAI;4CAAI,OAAO;gDAAE,cAAc;4CAAE;sDACpC,cAAA,2BAAC,wBAAe;;;;;;;;;;sDAWlB,2BAAC,SAAG;4CACF,IAAI;4CACJ,IAAI;4CACJ,IAAI;4CACJ,IAAI;4CACJ,IAAI;4CACJ,KAAK;4CACL,OAAO;gDAAE,cAAc;4CAAE;sDAEzB,cAAA,2BAAC,uBAAc;;;;;;;;;;sDAYjB,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,KAAK;sDAChD,cAAA,2BAAC,qBAAY;;;;;;;;;;;;;;;;;;;;;;;;;;sCAgBrB,2BAAC,oBAAe;;;;;sCAKhB,2BAAC;4BACC,MAAM;4BACN,SAAS,IAAM,wBAAwB;;;;;;;;YAI/C;eA5JM;;oBAQ8B,aAAQ;;;iBARtC;gBA8JN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID/LD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACnoB"}